package com.stpl.tech.scm.data.transport.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FullfillmentDataWarehouseLevel {
    String transferingUnit;
    Double avgFPer;
    Double  avgImFPer;
    Double criticalAvg;
    Double criticalProductFF;
    String isCriticalProd;
    Double bakeryFP;
    Double withoutBakeryAvgFPer;
    Double  productLevel1FFPer = 0.0;
    Double  productLevel2FFPer = 0.0;
    Double  productLevel3FFPer = 0.0;
    Double  productLevel4FFPer = 0.0;
    Double  productLevel5FFPer = 0.0;
}
