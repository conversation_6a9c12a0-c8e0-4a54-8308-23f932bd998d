package com.stpl.tech.scm.data.transport.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FullfillmentData {

    String transferringUnit;
    Long transferringUnitId;
    String requestingUnit;
    Long requestingUnitId;
    Long requestOrderId;
    Date lastUpdateTime;
    Long requestOrderItemId;
          String  productName;
    Long productId;
         String  unitOfMeasure;
    Double requestedAbsoluteQuantity;
    Double  transferredQuantity;
    Double receivedQuantity;
    String  isCritical;
    Double fullfillmentPercentage;
    Double   impactedFullfillmentPercentage;
    String isImpacted;
    Set<Long> impactedScmProducts;
    Boolean isBakeryProduct = false;
    Boolean bakeryProductFP;

    String productLevel;
}
